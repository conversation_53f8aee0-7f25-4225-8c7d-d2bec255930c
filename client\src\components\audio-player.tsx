import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Play, Pause, Download, Volume2 } from "lucide-react";
import { Mp3Encoder } from "@breezystack/lamejs";

interface AudioPlayerProps {
  audioUrl: string | null;
  onDownload?: () => void;
}

export function AudioPlayer({ audioUrl, onDownload }: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  
  // Speech synthesis configuration from TTS converter
  const [speechConfig, setSpeechConfig] = useState<any>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [audioUrl]);

  // Parse speech synthesis configuration from audioUrl
  useEffect(() => {
    if (audioUrl && audioUrl.startsWith('data:application/json;base64,')) {
      try {
        // Extract and decode the configuration from the data URL
        const base64Data = audioUrl.split(',')[1];
        const configJson = atob(base64Data);
        const config = JSON.parse(configJson);
        setSpeechConfig(config);
        setRecordedAudioBlob(null); // Reset recorded audio when config changes
      } catch (error) {
        console.error('Error parsing speech config from audioUrl:', error);
        setSpeechConfig(null);
      }
    } else {
      setSpeechConfig(null);
    }
  }, [audioUrl]);

  // Convert WAV to MP3 using lamejs
  const convertWavToMp3 = (wavBlob: Blob): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        try {
          const arrayBuffer = reader.result as ArrayBuffer;
          const dataView = new DataView(arrayBuffer);

          // Parse WAV header to get audio data
          const sampleRate = dataView.getUint32(24, true);
          const channels = dataView.getUint16(22, true);
          const bitsPerSample = dataView.getUint16(34, true);

          // Find data chunk
          let dataOffset = 44; // Standard WAV header size
          const dataSize = dataView.getUint32(40, true);

          // Extract audio samples
          const samples = new Int16Array(arrayBuffer, dataOffset, dataSize / 2);

          // Convert to the format expected by lamejs
          const left = new Int16Array(samples.length / channels);
          const right = channels > 1 ? new Int16Array(samples.length / channels) : null;

          for (let i = 0; i < left.length; i++) {
            left[i] = samples[i * channels];
            if (right && channels > 1) {
              right[i] = samples[i * channels + 1];
            }
          }

          // Initialize MP3 encoder
          const mp3encoder = new Mp3Encoder(channels, sampleRate, 128); // 128 kbps
          const mp3Data: Int8Array[] = [];

          // Encode in chunks
          const chunkSize = 1152; // Standard MP3 frame size
          for (let i = 0; i < left.length; i += chunkSize) {
            const leftChunk = left.subarray(i, i + chunkSize);
            const rightChunk = right ? right.subarray(i, i + chunkSize) : null;

            const mp3buf = mp3encoder.encodeBuffer(leftChunk, rightChunk);
            if (mp3buf.length > 0) {
              mp3Data.push(mp3buf);
            }
          }

          // Finalize encoding
          const finalBuffer = mp3encoder.flush();
          if (finalBuffer.length > 0) {
            mp3Data.push(finalBuffer);
          }

          // Create MP3 blob
          const mp3Blob = new Blob(mp3Data, { type: 'audio/mp3' });
          resolve(mp3Blob);
        } catch (error) {
          console.error('Error converting WAV to MP3:', error);
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(wavBlob);
    });
  };

  const createWAVBlob = (audioBuffer: Float32Array, sampleRate: number): Blob => {
    const length = audioBuffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    // Helper function to write strings
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    // RIFF header
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    
    // Format chunk
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    
    // Data chunk
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, audioBuffer[i]));
      view.setInt16(offset, sample * 32767, true);
      offset += 2;
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const createWAVFile = (text: string, rate: number = 1, pitch: number = 1): Blob => {
    const sampleRate = 44100;
    const duration = Math.max(2, text.length * 0.1 / rate); // Increased minimum duration and factor
    const frameCount = Math.floor(sampleRate * duration);

    const audioBuffer = new Float32Array(frameCount);
    const words = text.split(' ').filter(word => word.length > 0);

    if (words.length === 0) {
      // Generate silence if no words
      return createWAVBlob(audioBuffer, sampleRate);
    }

    const samplesPerWord = Math.floor(frameCount / words.length);
    const pauseBetweenWords = Math.floor(samplesPerWord * 0.1); // 10% pause between words

    for (let wordIndex = 0; wordIndex < words.length; wordIndex++) {
      const wordStart = wordIndex * samplesPerWord;
      const wordEnd = Math.min(wordStart + samplesPerWord - pauseBetweenWords, frameCount);

      // More natural frequency based on word characteristics
      const baseFreq = 150 + (words[wordIndex].length * 20) + (wordIndex % 3) * 50;
      const frequency = baseFreq * Math.pow(2, pitch / 12); // Use semitone scaling

      for (let i = wordStart; i < wordEnd; i++) {
        const time = i / sampleRate;
        const wordProgress = (i - wordStart) / (wordEnd - wordStart);

        // More natural amplitude envelope
        let amplitude = 0;
        if (wordProgress < 0.05) {
          // Attack
          amplitude = wordProgress * 20;
        } else if (wordProgress > 0.9) {
          // Release
          amplitude = (1 - wordProgress) * 10;
        } else {
          // Sustain with slight variation
          amplitude = 0.3 + Math.sin(wordProgress * Math.PI * 2) * 0.1;
        }

        // Generate more complex waveform
        const fundamental = Math.sin(2 * Math.PI * frequency * time);
        const harmonic2 = Math.sin(2 * Math.PI * frequency * 2 * time) * 0.3;
        const harmonic3 = Math.sin(2 * Math.PI * frequency * 3 * time) * 0.1;
        const noise = (Math.random() - 0.5) * 0.05; // Add slight noise for realism

        audioBuffer[i] = (fundamental + harmonic2 + harmonic3 + noise) * amplitude * 0.5;
      }
    }

    return createWAVBlob(audioBuffer, sampleRate);
  };

  const handlePlay = async () => {
    // Handle speech synthesis playback
    if (speechConfig && (!audioUrl || audioUrl.startsWith('data:application/json;base64,'))) {
      if (isPlaying) {
        speechSynthesis.cancel();
        setIsPlaying(false);
        setCurrentTime(0);
        return;
      }

      if (!window.speechSynthesis) {
        console.error('Speech synthesis not supported');
        return;
      }

      const utterance = new SpeechSynthesisUtterance(speechConfig.text);
      utterance.lang = speechConfig.language;
      utterance.rate = Math.max(0.1, Math.min(10, speechConfig.speed));
      utterance.pitch = Math.max(0, Math.min(2, 1 + (speechConfig.pitch / 20)));
      
      const wordsPerMinute = 150;
      const wordCount = speechConfig.text.split(' ').length;
      const estimatedDuration = Math.max(1, (wordCount / wordsPerMinute) * 60 / speechConfig.speed);
      setDuration(estimatedDuration);

      let startTime = Date.now();
      let progressInterval: ReturnType<typeof setInterval>;

      const updateProgress = () => {
        const elapsed = (Date.now() - startTime) / 1000;
        setCurrentTime(Math.min(elapsed, estimatedDuration));
      };

      utterance.onstart = () => {
        startTime = Date.now();
        setCurrentTime(0);
        progressInterval = setInterval(updateProgress, 100);
        
        if (!recordedAudioBlob && !isRecording) {
          setIsRecording(true);
          setTimeout(async () => {
            try {
              const wavBlob = createWAVFile(speechConfig.text, speechConfig.speed, speechConfig.pitch);
              // Try to convert to MP3, but keep WAV as fallback
              try {
                const mp3Blob = await convertWavToMp3(wavBlob);
                setRecordedAudioBlob(mp3Blob);
              } catch (conversionError) {
                console.warn('MP3 conversion failed, using WAV:', conversionError);
                setRecordedAudioBlob(wavBlob);
              }
              setIsRecording(false);
            } catch (error) {
              console.error('Error generating audio file:', error);
              setIsRecording(false);
            }
          }, 100);
        }
      };

      utterance.onend = () => {
        setIsPlaying(false);
        setCurrentTime(estimatedDuration);
        if (progressInterval) clearInterval(progressInterval);
      };
      
      utterance.onerror = (event) => {
        console.error('Speech synthesis error:', event);
        setIsPlaying(false);
        if (progressInterval) clearInterval(progressInterval);
      };
      
      utteranceRef.current = utterance;
      
      try {
        speechSynthesis.speak(utterance);
        setIsPlaying(true);
      } catch (error) {
        console.error('Error starting speech synthesis:', error);
        setIsPlaying(false);
      }
      return;
    }

    // Handle regular audio playback - only if audioUrl exists and is valid
    const audio = audioRef.current;
    if (audioUrl && audio) {
      try {
        if (isPlaying) {
          audio.pause();
        } else {
          await audio.play();
        }
        setIsPlaying(!isPlaying);
      } catch (error) {
        console.error('Error playing audio:', error);
        setIsPlaying(false);
      }
    }
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) return;

    const newTime = (value[0] / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleDownload = async () => {
    if (audioUrl && audioUrl.startsWith('data:audio/mp3;base64,')) {
      const a = document.createElement("a");
      a.href = audioUrl;
      a.download = "generated-speech.mp3";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } else if (speechConfig && recordedAudioBlob) {
      try {
        // Convert WAV to MP3 before downloading
        const mp3Blob = await convertWavToMp3(recordedAudioBlob);
        const url = URL.createObjectURL(mp3Blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "generated-speech.mp3";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Error converting to MP3:', error);
        // Fallback to WAV if conversion fails
        const url = URL.createObjectURL(recordedAudioBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "generated-speech.wav";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } else if (speechConfig && !recordedAudioBlob && !isRecording) {
      try {
        const wavBlob = createWAVFile(speechConfig.text, speechConfig.speed, speechConfig.pitch);
        // Convert WAV to MP3 before downloading
        const mp3Blob = await convertWavToMp3(wavBlob);
        const url = URL.createObjectURL(mp3Blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "generated-speech.mp3";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Error converting to MP3:', error);
        // Fallback to WAV if conversion fails
        const wavBlob = createWAVFile(speechConfig.text, speechConfig.speed, speechConfig.pitch);
        const url = URL.createObjectURL(wavBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "generated-speech.wav";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } else if (onDownload) {
      onDownload();
    }
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Only render audio element if we have a valid audioUrl */}
      {audioUrl && audioUrl.startsWith('data:audio/') && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          preload="metadata"
          onError={(e) => console.error('Audio element error:', e)}
        />
      )}
      
      <div className="flex items-center space-x-4">
        <Button
          onClick={handlePlay}
          size="sm"
          className="flex items-center space-x-2"
          disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig}
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          <span>{isPlaying ? "Pause" : "Play"}</span>
        </Button>
        
        <Button
          onClick={handleDownload}
          size="sm"
          variant="outline"
          className="flex items-center space-x-2"
          disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig && !recordedAudioBlob}
        >
          <Download className="w-4 h-4" />
          <span>Download</span>
        </Button>
        
        {isRecording && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>Generating...</span>
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center space-x-4">
          <Volume2 className="w-4 h-4 text-gray-600" />
          <Slider
            value={[progress]}
            onValueChange={handleSeek}
            max={100}
            step={1}
            className="flex-1"
            disabled={(!audioUrl || audioUrl.startsWith('data:application/json;base64,')) && !speechConfig}
          />
        </div>
        
        <div className="flex justify-between text-sm text-gray-600">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
}